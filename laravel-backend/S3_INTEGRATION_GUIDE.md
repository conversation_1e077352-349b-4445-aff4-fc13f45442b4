# AWS S3 Integration Guide

## Overview
This guide explains how to use the AWS S3 integration for image storage in your Laravel project.

## Setup Instructions

### 1. Configure AWS Credentials
Update your `.env` file with your AWS S3 credentials:

```env
# Change filesystem disk to S3
FILESYSTEM_DISK=s3

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your_bucket_name_here
AWS_URL=
AWS_ENDPOINT=
AWS_USE_PATH_STYLE_ENDPOINT=false
```

### 2. Run Migration
Run the migration to add the S3 tracking column:

```bash
php artisan migrate
```

### 3. Migrate Existing Images (Optional)
If you have existing product images in local storage, migrate them to S3:

```bash
# Dry run to see what would be migrated
php artisan migrate:products-to-s3 --dry-run

# Actual migration
php artisan migrate:products-to-s3
```

## API Endpoints

### Product Endpoints (Updated for S3)
- `POST /api/products` - Create product with S3 image upload
- `PUT /api/products/{id}` - Update product with S3 image upload
- `DELETE /api/products/{id}` - Delete product and S3 image

### Image Management Endpoints
- `POST /api/images/upload` - Upload single image to S3
- `POST /api/images/upload-multiple` - Upload multiple images to S3
- `DELETE /api/images/delete` - Delete image from S3
- `GET /api/images/url` - Get S3 URL for image
- `GET /api/images/list` - List images in S3 folder

## Usage Examples

### 1. Upload Product with Image
```javascript
const formData = new FormData();
formData.append('categories_id', '1');
formData.append('brands_id', '1');
formData.append('product_name', 'Test Product');
formData.append('description', 'Product description');
formData.append('quantity', '10');
formData.append('price', '99.99');
formData.append('image', imageFile); // File object
formData.append('status', '1');

fetch('/api/products', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: formData
})
.then(response => response.json())
.then(data => {
    console.log('Product created:', data);
    // data.data.image contains S3 path
    // data.data.image_url contains full S3 URL
});
```

### 2. Upload Single Image
```javascript
const formData = new FormData();
formData.append('image', imageFile);
formData.append('folder', 'products'); // optional

fetch('/api/images/upload', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: formData
})
.then(response => response.json())
.then(data => {
    console.log('Image uploaded:', data);
    // data.data.path contains S3 path
    // data.data.url contains full S3 URL
});
```

### 3. Get Product with S3 Image URL
```javascript
fetch('/api/products/1', {
    headers: {
        'Authorization': 'Bearer ' + token
    }
})
.then(response => response.json())
.then(data => {
    console.log('Product:', data);
    // data.data.image contains S3 path
    // data.data.image_url contains full S3 URL for display
});
```

### 4. Delete Image from S3
```javascript
fetch('/api/images/delete', {
    method: 'DELETE',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        path: 'products/image_name.jpg'
    })
})
.then(response => response.json())
.then(data => {
    console.log('Image deleted:', data);
});
```

## Product Model Features

### Automatic Image URL Generation
The Product model automatically generates S3 URLs:

```php
$product = Product::find(1);
echo $product->image;     // S3 path: products/image_name.jpg
echo $product->image_url; // Full S3 URL: https://bucket.s3.region.amazonaws.com/products/image_name.jpg
```

### Helper Methods
```php
$product = Product::find(1);

// Check if product has image
if ($product->hasImage()) {
    echo "Product has image";
}

// Get image filename only
echo $product->image_name; // image_name.jpg
```

## S3Service Methods

### Upload Files
```php
$s3Service = app(S3Service::class);

// Upload single file
$path = $s3Service->uploadFile($uploadedFile, 'products');

// Upload multiple files
$paths = $s3Service->uploadMultipleFiles($uploadedFiles, 'products');
```

### File Operations
```php
// Delete file
$s3Service->deleteFile('products/image.jpg');

// Get file URL
$url = $s3Service->getFileUrl('products/image.jpg');

// Check if file exists
$exists = $s3Service->fileExists('products/image.jpg');

// Get file size
$size = $s3Service->getFileSize('products/image.jpg');
```

## Configuration

### S3 Settings (config/s3.php)
```php
'folders' => [
    'products' => 'products',
    'categories' => 'categories',
    'profiles' => 'profiles',
],

'allowed_extensions' => [
    'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
],

'max_file_size' => 2048, // KB
```

## Troubleshooting

### Common Issues

1. **AWS Credentials Error**
   - Verify your AWS credentials in `.env`
   - Ensure your AWS user has S3 permissions

2. **Bucket Access Error**
   - Check bucket name in `.env`
   - Verify bucket permissions and CORS settings

3. **File Upload Fails**
   - Check file size limits
   - Verify allowed file extensions
   - Check Laravel logs for detailed errors

### Testing S3 Connection
```bash
# Test S3 connection
php artisan tinker
>>> Storage::disk('s3')->put('test.txt', 'Hello S3');
>>> Storage::disk('s3')->get('test.txt');
>>> Storage::disk('s3')->delete('test.txt');
```

## Security Notes

1. **Bucket Permissions**: Ensure your S3 bucket has appropriate read/write permissions
2. **CORS Configuration**: Configure CORS if accessing from web browsers
3. **File Validation**: Always validate file types and sizes before upload
4. **Access Keys**: Keep AWS credentials secure and rotate regularly

## Migration Notes

- The migration adds a `migrated_to_s3` column to track migration status
- Use the Artisan command to migrate existing images safely
- The migration command supports dry-run mode for testing
- Local files can be automatically deleted after successful S3 upload
