<?php

return [
    /*
    |--------------------------------------------------------------------------
    | S3 Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for AWS S3 integration
    |
    */

    'default_folder' => env('S3_DEFAULT_FOLDER', 'uploads'),
    
    'folders' => [
        'products' => 'products',
        'categories' => 'categories',
        'profiles' => 'profiles',
        'brands' => 'brands',
        'general' => 'uploads'
    ],

    'allowed_extensions' => [
        'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
        'documents' => ['pdf', 'doc', 'docx', 'txt'],
        'all' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'pdf', 'doc', 'docx', 'txt']
    ],

    'max_file_size' => env('S3_MAX_FILE_SIZE', 2048), // in KB

    'image_quality' => env('S3_IMAGE_QUALITY', 85), // for image compression

    'generate_thumbnails' => env('S3_GENERATE_THUMBNAILS', false),

    'thumbnail_sizes' => [
        'small' => [150, 150],
        'medium' => [300, 300],
        'large' => [600, 600]
    ],

    'cache_control' => env('S3_CACHE_CONTROL', 'max-age=31536000'), // 1 year

    'metadata' => [
        'CacheControl' => env('S3_CACHE_CONTROL', 'max-age=31536000'),
        'ContentType' => 'auto-detect'
    ],

    'url_expiration' => env('S3_URL_EXPIRATION', 3600), // in seconds, for signed URLs

    'public_read' => env('S3_PUBLIC_READ', true), // make files publicly readable

    'backup_to_local' => env('S3_BACKUP_TO_LOCAL', false), // also save to local storage

    'delete_local_after_upload' => env('S3_DELETE_LOCAL_AFTER_UPLOAD', true),
];
