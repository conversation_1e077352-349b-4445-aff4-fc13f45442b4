<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

class S3Service
{
    /**
     * Upload file to S3 bucket
     *
     * @param UploadedFile $file
     * @param string $folder
     * @return string|false
     */
    public function uploadFile(UploadedFile $file, string $folder = 'public')
    {
        try {
            // Generate unique filename
            $filename = $this->generateUniqueFilename($file, $folder);

            // Upload to S3
            $path = Storage::disk('s3')->putFileAs($folder, $file, $filename);

            return $path;
        } catch (\Exception $e) {
            Log::error('S3 Upload Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete file from S3 bucket
     *
     * @param string $filePath
     * @return bool
     */
    public function deleteFile(string $filePath): bool
    {
        try {
            if (Storage::disk('s3')->exists($filePath)) {
                return Storage::disk('s3')->delete($filePath);
            }
            return true; // File doesn't exist, consider it deleted
        } catch (\Exception $e) {
            Log::error('S3 Delete Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get public URL for S3 file
     *
     * @param string $filePath
     * @return string
     */
    public function getFileUrl(string $filePath): string
    {
        try {
            // For S3, construct the URL manually since url() method might not be available
            $bucket = env('AWS_BUCKET');
            $region = env('AWS_DEFAULT_REGION');
            return "https://{$bucket}.s3.{$region}.amazonaws.com/{$filePath}";
        } catch (\Exception $e) {
            Log::error('S3 URL Error: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Check if file exists in S3
     *
     * @param string $filePath
     * @return bool
     */
    public function fileExists(string $filePath): bool
    {
        try {
            return Storage::disk('s3')->exists($filePath);
        } catch (\Exception $e) {
            Log::error('S3 File Check Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate unique filename for upload
     *
     * @param UploadedFile $file
     * @param string $folder
     * @return string
     */
    private function generateUniqueFilename(UploadedFile $file, string $folder): string
    {
        $extension = $file->getClientOriginalExtension();
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $timestamp = now()->format('Y-m-d_H-i-s');
        $randomString = Str::random(8);

        return "{$originalName}_{$timestamp}_{$randomString}.{$extension}";
    }

    /**
     * Upload multiple files to S3
     *
     * @param array $files
     * @param string $folder
     * @return array
     */
    public function uploadMultipleFiles(array $files, string $folder = 'public'): array
    {
        $uploadedFiles = [];

        foreach ($files as $file) {
            if ($file instanceof UploadedFile) {
                $path = $this->uploadFile($file, $folder);
                if ($path) {
                    $uploadedFiles[] = $path;
                }
            }
        }

        return $uploadedFiles;
    }

    /**
     * Get file size from S3
     *
     * @param string $filePath
     * @return int|false
     */
    public function getFileSize(string $filePath)
    {
        try {
            return Storage::disk('s3')->size($filePath);
        } catch (\Exception $e) {
            Log::error('S3 File Size Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Copy file within S3 bucket
     *
     * @param string $fromPath
     * @param string $toPath
     * @return bool
     */
    public function copyFile(string $fromPath, string $toPath): bool
    {
        try {
            return Storage::disk('s3')->copy($fromPath, $toPath);
        } catch (\Exception $e) {
            Log::error('S3 Copy Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * List files in a specific folder
     *
     * @param string $folder
     * @return array
     */
    public function listFiles(string $folder = ''): array
    {
        try {
            $files = Storage::disk('s3')->files($folder);
            $fileList = [];

            foreach ($files as $file) {
                $fileList[] = [
                    'path' => $file,
                    'url' => $this->getFileUrl($file),
                    'size' => $this->getFileSize($file),
                    'name' => basename($file),
                    'last_modified' => Storage::disk('s3')->lastModified($file)
                ];
            }

            return $fileList;
        } catch (\Exception $e) {
            Log::error('S3 List Files Error: ' . $e->getMessage());
            return [];
        }
    }
}
