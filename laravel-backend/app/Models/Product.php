<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class Product extends Model
{
    protected $fillable = [
        "categories_id",
        "brands_id",
        "product_name",
        "description",
        "quantity",
        "price",
        "status",
        "image",
    ];

    public function categories()
    {
        return $this->belongsTo(Category::class, 'categories_id');
    }

    public function brands()
    {
        return $this->belongsTo(Brand::class, 'brands_id');
    }

    /**
     * Get the full S3 URL for the product image
     *
     * @return string|null
     */
    public function getImageUrlAttribute()
    {
        if (!$this->image) {
            return null;
        }

        // If it's already a full URL (S3), return as is
        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        // Generate S3 URL
        try {
            return Storage::disk('s3')->url($this->image);
        } catch (\Exception $e) {
            Log::error('Error generating S3 URL for product image: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if the product has an image
     *
     * @return bool
     */
    public function hasImage(): bool
    {
        return !empty($this->image);
    }

    /**
     * Get image file name without path
     *
     * @return string|null
     */
    public function getImageNameAttribute()
    {
        if (!$this->image) {
            return null;
        }

        return basename($this->image);
    }

    /**
     * Override toArray to include image_url
     *
     * @return array
     */
    public function toArray()
    {
        $array = parent::toArray();
        $array['image_url'] = $this->image_url;
        return $array;
    }
}
