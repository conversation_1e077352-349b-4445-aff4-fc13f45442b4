<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;
use App\Services\S3Service;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class MigrateProductImagesToS3 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:products-to-s3 {--dry-run : Show what would be migrated without actually doing it}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing product images from local storage to S3';

    protected $s3Service;

    public function __construct(S3Service $s3Service)
    {
        parent::__construct();
        $this->s3Service = $s3Service;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No files will be actually migrated');
        }

        $this->info('🚀 Starting migration of product images to S3...');

        // Get all products with images that haven't been migrated
        $products = Product::whereNotNull('image')
                          ->where('migrated_to_s3', false)
                          ->get();

        if ($products->isEmpty()) {
            $this->info('✅ No products found that need migration.');
            return;
        }

        $this->info("📊 Found {$products->count()} products with images to migrate.");

        $progressBar = $this->output->createProgressBar($products->count());
        $progressBar->start();

        $migrated = 0;
        $failed = 0;

        foreach ($products as $product) {
            try {
                $localPath = storage_path('app/public/' . $product->image);

                // Check if local file exists
                if (!File::exists($localPath)) {
                    $this->newLine();
                    $this->warn("⚠️  Local file not found for product {$product->id}: {$localPath}");
                    $progressBar->advance();
                    $failed++;
                    continue;
                }

                if (!$isDryRun) {
                    // Create a temporary uploaded file object
                    $tempFile = new \Illuminate\Http\UploadedFile(
                        $localPath,
                        basename($product->image),
                        File::mimeType($localPath),
                        null,
                        true
                    );

                    // Upload to S3
                    $s3Path = $this->s3Service->uploadFile($tempFile, 'products');

                    if ($s3Path) {
                        // Update product with new S3 path
                        $product->update([
                            'image' => $s3Path,
                            'migrated_to_s3' => true
                        ]);

                        // Optionally delete local file
                        if (config('s3.delete_local_after_upload', true)) {
                            Storage::disk('public')->delete($product->image);
                        }

                        $migrated++;
                    } else {
                        $this->newLine();
                        $this->error("❌ Failed to upload to S3 for product {$product->id}");
                        $failed++;
                    }
                } else {
                    // Dry run - just show what would be migrated
                    $this->newLine();
                    $this->line("📁 Would migrate: {$product->image} for product {$product->id}");
                    $migrated++;
                }

            } catch (\Exception $e) {
                $this->newLine();
                $this->error("❌ Error migrating product {$product->id}: " . $e->getMessage());
                $failed++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        if ($isDryRun) {
            $this->info("🔍 DRY RUN COMPLETE:");
            $this->info("   - Would migrate: {$migrated} products");
            $this->info("   - Would fail: {$failed} products");
            $this->info("Run without --dry-run to perform actual migration.");
        } else {
            $this->info("✅ MIGRATION COMPLETE:");
            $this->info("   - Successfully migrated: {$migrated} products");
            $this->info("   - Failed: {$failed} products");
        }

        return 0;
    }
}
