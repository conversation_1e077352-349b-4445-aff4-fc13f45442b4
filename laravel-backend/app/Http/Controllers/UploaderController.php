<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;

class UploaderController extends Controller
{
    public function __invoke(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|mimes:jpeg,png,jpg,gif,pdf,doc,docx|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if file exists in request
        if (!$request->hasFile('image')) {
            return response()->json([
                'success' => false,
                'message' => 'No file provided'
            ], 400);
        }

        $file = $request->file('image');

        // Check if file is valid
        if (!$file->isValid()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid file upload'
            ], 400);
        }

        try {
            // Generate unique filename
            $extension = $file->getClientOriginalExtension();
            $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $timestamp = now()->format('Y-m-d_H-i-s');
            $randomString = \Illuminate\Support\Str::random(8);
            $filename = "{$originalName}_{$timestamp}_{$randomString}.{$extension}";

            // Store the file with custom filename to S3
            $path = $file->storeAs('uploads', $filename, 's3');

            return response()->json([
                'success' => true,
                'path' => "https://laravelbucket2025.s3.amazonaws.com/{$path}",
                'filename' => $filename,
                'original_name' => $file->getClientOriginalName(),
                'message' => 'File uploaded successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file: ' . $e->getMessage()
            ], 500);
        }
    }
}
