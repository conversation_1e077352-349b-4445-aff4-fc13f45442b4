<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class UploaderController extends Controller
{
    public function __invoke(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|mimes:jpeg,png,jpg,gif,pdf,doc,docx|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if file exists in request
        if (!$request->hasFile('image')) {
            return response()->json([
                'success' => false,
                'message' => 'No file provided'
            ], 400);
        }

        $file = $request->file('image');

        // Check if file is valid
        if (!$file->isValid()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid file upload'
            ], 400);
        }

        try {
            // Generate unique filename
            $extension = $file->getClientOriginalExtension();
            $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $timestamp = now()->format('Y-m-d_H-i-s');
            $randomString = \Illuminate\Support\Str::random(8);
            $filename = "{$originalName}_{$timestamp}_{$randomString}.{$extension}";

            // Store the file with custom filename to S3 public folder
            $path = $file->storeAs('public', $filename, 's3');

            // Debug: Log the path to see what's returned
            Log::info('Upload path returned: ' . $path);
            Log::info('Generated filename: ' . $filename);

            // Ensure we have a valid path
            if (!$path) {
                throw new \Exception('Failed to store file - no path returned');
            }

            // Construct the full URL
            $fullUrl = "https://laravelbucket2025.s3.amazonaws.com/{$path}";

            return response()->json([
                'success' => true,
                'path' => $fullUrl,
                'filename' => $filename,
                'original_name' => $file->getClientOriginalName(),
                'stored_path' => $path,
                'message' => 'File uploaded successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Upload error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file: ' . $e->getMessage(),
                'error_details' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Test S3 connectivity
     */
    public function testS3()
    {
        try {
            // Test basic S3 connection
            $disk = Storage::disk('s3');

            // Try to list files in the bucket
            $files = $disk->files('');

            // Try to create a test file
            $testContent = 'Test file created at ' . now();
            $testPath = $disk->put('test/test.txt', $testContent);

            return response()->json([
                'success' => true,
                'message' => 'S3 connection successful',
                'bucket' => env('AWS_BUCKET'),
                'region' => env('AWS_DEFAULT_REGION'),
                'files_count' => count($files),
                'test_file_created' => $testPath,
                'config' => [
                    'driver' => config('filesystems.disks.s3.driver'),
                    'bucket' => config('filesystems.disks.s3.bucket'),
                    'region' => config('filesystems.disks.s3.region'),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'S3 connection failed',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
}
