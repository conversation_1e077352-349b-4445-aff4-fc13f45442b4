<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;

class UploaderController extends Controller
{
    public function __invoke(Request $request)
    {
        $path = $request->file('file')->storePublicly('public');
        return response()->json([
            'path' => "https://laravelbucket2025.s3.amazonaws.com/$path",
            'msg'  => 'File uploaded successfully'
        ]);
    }
}
