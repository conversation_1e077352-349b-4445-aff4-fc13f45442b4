<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use App\Services\S3Service;

class ProductController extends Controller
{
    protected $s3Service;

    public function __construct(S3Service $s3Service)
    {
        $this->s3Service = $s3Service;
    }
    public function index(Request  $request)
    {

        $product = Product::query();
        if ($request->has("id")) {
            $product->where("id", "=", $request->input("id"));
        }
        if ($request->has("categories_id")) {
            $product->where("categories_id", "=", $request->input("categories_id"));
        }
        if ($request->has("brands_id")) {
            $product->where("brands_id", "=", $request->input("brands_id"));
        }
        if ($request->has("text_search")) {
            $product->where("product_name", "LIKE", "%" . $request->input("text_search") . "%");
        }
        if ($request->has("status")) {
            $product->where("status", "=", $request->input("status"));
        }
        $product->with(["categories", "brands"])->paginate();


        $category = DB::select('select * from categories');
        $brand = DB::select('select * from brands');


        return [
            "list" => $product->get(),
            "categories" => $category,
            "brands" => $brand
        ];
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {


        // form data //file image
        $request->validate([
            'categories_id' => 'required|exists:categories,id',
            'brands_id' => 'required|exists:brands,id',
            'product_name' => 'required|string',
            'description' => 'nullable|string',
            'quantity' => 'required|integer',
            'price' => 'required|numeric',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'boolean'
        ]);
        $data = $request->all();
        if ($request->hasFile('image')) {
            $imagePath = $this->s3Service->uploadFile($request->file('image'), 'products');
            if ($imagePath) {
                $data['image'] = $imagePath;
            } else {
                return response()->json([
                    "error" => "Failed to upload image to S3",
                    "message" => "Image upload failed"
                ], 500);
            }
        }
        $product = Product::create($data);
        return response()->json([
            "data" => $product,
            "message" => "Save succesfully!"
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $product = Product::find($id);
        return response()->json([
            "data" => $product->load(['categories', 'brands'])
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $product = Product::find($id);
        $request->validate([
            'categories_id' => 'required|exists:categories,id',
            'brands_id' => 'required|exists:brands,id',
            'product_name' => 'required|string',
            'description' => 'nullable|string',
            'quantity' => 'required|integer',
            'price' => 'required|numeric',
            'image' => 'nullable|image|max:2048',
            'status' => 'boolean'
        ]);
        $data = $request->all();
        if ($request->hasFile('image')) {
            // Delete old image from S3 if exists
            if ($product->image) {
                $this->s3Service->deleteFile($product->image);
            }

            // Upload new image to S3
            $imagePath = $this->s3Service->uploadFile($request->file('image'), 'products');
            if ($imagePath) {
                $data['image'] = $imagePath;
            } else {
                return response()->json([
                    "error" => "Failed to upload image to S3",
                    "message" => "Image upload failed"
                ], 500);
            }
        }

        if ($request->remove_image) {
            // Delete image from S3
            if ($product->image) {
                $this->s3Service->deleteFile($product->image);
            }
            $data['image'] = null;
        }

        $product->update($data);
        return response()->json([
            "data" => $product,
            "message" => "Update succesfully!"
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $product = Product::findOrFail($id);
            if ($product->image) {
                $this->s3Service->deleteFile($product->image);
            }
            $product->delete();

            return response()->json([
                "success" => true,
                "message" => "Product deleted successfully"
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                "success" => false,
                "message" => "Failed to delete product",
                "error" => $e->getMessage()
            ], 500);
        }
    }
}
