<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('brands', function (Blueprint $table) {
            $table->id(); //FK
            $table->string('name', 100);
            $table->string('code', 10)->unique();
            $table->string('from_country', 50);
            $table->string('description', 255)->nullable();
            $table->boolean('status')->default(1);
            $table->timestamps(); //created_at, updated_at
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('brands');
    }
};
