<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * This migration helps transition existing products from local storage to S3
     */
    public function up(): void
    {
        // Add a column to track migration status if needed
        Schema::table('products', function (Blueprint $table) {
            $table->boolean('migrated_to_s3')->default(false)->after('image');
        });

        // Note: The actual migration of files should be done via an Artisan command
        // to avoid timeout issues during migration
        // You can create a command: php artisan make:command MigrateProductImagesToS3
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('migrated_to_s3');
        });
    }
};
