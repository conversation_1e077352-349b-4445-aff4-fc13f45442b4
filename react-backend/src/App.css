@tailwind base;
@tailwind components;
@tailwind utilities;
/* General styles */
body {
  font-family: Arial, sans-serif;
  background-color: #f8f9fa;
  margin: 0;
  padding: 0;
}

/* Layout container */
.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  text-align: center;
}

/* Header */
.header {
  background-color: #007bff;
  color: white;
  padding: 15px;
}

.header nav {
  margin-top: 10px;
}

.nav-link {
  color: white;
  text-decoration: none;
  margin: 0 10px;
}

.nav-link:hover {
  text-decoration: underline;
}

/* Main content */
.main-content {
  flex: 1;
  padding: 20px;
}

/* Footer */
.footer {
  background-color: #343a40;
  color: white;
  padding: 10px;
}

/* Error Page */
.error-page {
  text-align: center;
  padding: 50px;
}

/* Button */
.button {
  display: inline-block;
  padding: 10px 15px;
  background-color: #007bff;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  margin-top: 10px;
}

.button:hover {
  background-color: #0056b3;
}
