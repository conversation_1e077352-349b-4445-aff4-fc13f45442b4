@tailwind base;
@tailwind components;
@tailwind utilities;

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: khmer;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.navbar {
  display: flex;
  justify-content: space-between;
  /* align-items: center; */
  padding: 1em;
  background-color: #097fad;
  color: #ffffff;
}

.navbar a {
  color: #ffffff;
  text-decoration: none;
  margin: 0 1em;
}

.navbar a:hover {
  color: #646cff;
}

.navbar .logo {
  font-size: 1.5em;
  font-weight: bold;
}

.nav-list {
  list-style-type: none;
  padding: 0;
  display: flex;
  flex-direction: row;
  margin: 0;
  justify-content: center; /* Center the list items */
  text-align: center;
}

.nav-list li {
  margin: 0 1em;
  padding: 15px;

}

.nav-list li a {
  color: #ffffff;
  text-decoration: none;
  padding: 0.5em 1em;
  transition: background-color 0.3s, color 0.3s;
}

.nav-list li a:hover {
  background-color: #646cff;
  color: #1a1a1a;
  border-radius: 4px;
}
.h3{
  font-size: 2em;
  line-height: 1.1;
  color: #037898;
}
.Heading3 {
  font-size: 1.5em;
  line-height: 1.1;
  color: #037898;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 1s ease-in-out;
}